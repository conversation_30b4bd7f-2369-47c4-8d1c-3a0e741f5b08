#!/usr/bin/env python3
"""
Test script to verify structured JSON taxonomy implementation
"""

import json
from issue_detection_agent import format_expense_taxonomy, EXPENSE_SCHEMA

def test_structured_json_taxonomy():
    """Test that the structured JSON taxonomy works correctly."""
    
    print("🧪 Testing Structured JSON Taxonomy Implementation")
    print("=" * 60)
    
    try:
        # Test 1: Generate structured JSON taxonomy
        print("📋 Test 1: JSON Structure Generation")
        taxonomy_json_str = format_expense_taxonomy()
        
        print(f"   Generated JSON length: {len(taxonomy_json_str)} characters")
        print(f"   Starts with: {taxonomy_json_str[:50]}...")
        
        # Parse the JSON to verify it's valid
        try:
            taxonomy_data = json.loads(taxonomy_json_str)
            print("   ✅ Valid JSON structure generated")
        except json.JSONDecodeError as e:
            print(f"   ❌ Invalid JSON generated: {e}")
            return False
        
        # Test 2: Check JSON structure
        print(f"\n🔍 Test 2: JSON Content Structure")
        
        required_keys = ["title", "description", "fields"]
        for key in required_keys:
            if key in taxonomy_data:
                print(f"   ✅ Has '{key}' field")
            else:
                print(f"   ❌ Missing '{key}' field")
                return False
        
        # Test 3: Check fields content
        print(f"\n📊 Test 3: Fields Content")
        fields = taxonomy_data.get("fields", {})
        original_properties = EXPENSE_SCHEMA.get("properties", {})
        
        print(f"   Original schema fields: {len(original_properties)}")
        print(f"   JSON taxonomy fields: {len(fields)}")
        
        if len(fields) == len(original_properties):
            print("   ✅ All schema fields included")
        else:
            print("   ❌ Field count mismatch")
            return False
        
        # Check each field has required structure
        missing_fields = []
        for field_name in original_properties.keys():
            if field_name not in fields:
                missing_fields.append(field_name)
            else:
                field_data = fields[field_name]
                if "title" not in field_data or "description" not in field_data:
                    print(f"   ❌ Field '{field_name}' missing title or description")
                    return False
        
        if missing_fields:
            print(f"   ❌ Missing fields: {missing_fields}")
            return False
        else:
            print("   ✅ All fields have proper structure")
        
        # Test 4: Show sample JSON structure
        print(f"\n📄 Test 4: Sample JSON Output")
        print("   First field structure:")
        first_field_name = list(fields.keys())[0]
        first_field_data = fields[first_field_name]
        print(f"   '{first_field_name}': {{")
        print(f"     'title': '{first_field_data['title'][:50]}...'")
        print(f"     'description': '{first_field_data['description'][:100]}...'")
        print("   }")
        
        # Test 5: Verify prompt integration format
        print(f"\n🎯 Test 5: Prompt Integration")
        prompt_section = f"EXPENSE TAXONOMY (JSON):\n{taxonomy_json_str}"
        
        print(f"   Prompt section length: {len(prompt_section)} characters")
        print(f"   Has proper label: {'✅' if 'EXPENSE TAXONOMY (JSON):' in prompt_section else '❌'}")
        print(f"   JSON follows label: {'✅' if taxonomy_json_str in prompt_section else '❌'}")
        
        # Show first few lines of what goes into prompt
        print("   Prompt format preview:")
        lines = prompt_section.split('\n')[:5]
        for line in lines:
            print(f"   > {line}")
        
        print(f"\n🎉 Test Result: ✅ ALL TESTS PASSED!")
        print("🚀 Structured JSON taxonomy implementation is working correctly!")
        print("   - Valid JSON structure generated")
        print("   - All schema fields included with proper structure")
        print("   - Clear labeling for prompt integration")
        print("   - Machine-readable format for better AI processing")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    
    try:
        success = test_structured_json_taxonomy()
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
