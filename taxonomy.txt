Expense Management System Taxonomy
Here is a taxonomy to main data fields that will appear on an expense file (or invoice, receipt). Please note these are not the only fields that can be expected, there can be more. 
1. Supplier (Service Provider)
Definition: Entity that provides goods, services, or products in exchange for payment.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Payment recipient name somewhere
Business identifier (name, logo, or tax ID) must appear
MOST LIKELY
Company name at top of document
Header section with business logo or letterhead
MOST LIKELY
"Bill From" or "Sold By" sections
Dedicated supplier information areas
MOST LIKELY
Merchant name on credit card statements
Transaction details from payment processor
POSSIBLE
Footer information
Contact details or business registration
POSSIBLE
Payment details section
Banking or remittance information

Common File Labels/Synonyms
Vendor, Merchant, Seller, Provider, Company, "From:", "Billed By:", "Sold By:", "Service Provider:", Business Name, Organization, Entity, Contractor, or direct company names without labels (e.g., "Apple Inc.", "Delta Airlines", "Starbucks")
2. Consumer (Recipient)
Definition: Person, department, or entity that receives goods/services and is responsible for the expense.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Some form of customer/buyer identification
Must be identifiable somewhere on document
MOST LIKELY
"Bill To" or "Customer" information
Dedicated customer details section
MOST LIKELY
Employee name on receipts
Individual identification on transaction
MOST LIKELY
Cardholder name on statements
Payment method owner information
POSSIBLE
"Purchased By" or "Ordered By" sections
Alternative customer identification
POSSIBLE
Department codes or cost centers
Organizational unit identification

Common File Labels/Synonyms
Customer, Client, Buyer, Purchaser, Employee, "Bill To:", "Customer:", "Ordered By:", "Cardholder:", Department, Cost Center, Team, Individual, or direct names without labels (e.g., "John Smith", "Marketing Department", "ABC Corp")
3. ICP (Independent Contractor Program) Requirements
Definition: ICP is the local employer for EOR (Employer of Record) employees.
Critical Rule: If the local ICP REQUIRES that their details be listed on the expense files, then all required consumer details on the expense record must be the ICP details (not the individual employee details).
How to Recognize ICP Requirements on Files
Reliability
Location/Method
Description
Most Likely
ICP company name as "Bill To" or "Customer"
Local employer entity listed as consumer
Most Likely
ICP business registration details as consumer
Official business entity information
MOST LIKELY
ICP address/contact info in consumer section
Local company billing address
MOST LIKELY
Corporate billing address in consumer section
Business entity rather than individual
POSSIBLE
Mixed billing scenarios
Regional variations in ICP requirements

4. Transaction Amount
Definition: The monetary value of the expense.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Total amount somewhere on document
Must include currency denomination
MOST LIKELY
"Total:", "Amount Due:", "Grand Total:"
Standard amount labels
MOST LIKELY
Bottom right of invoice/receipt
Common amount placement
MOST LIKELY
Summary section
Calculation breakdown area
POSSIBLE
Line item totals only
Multiple amounts without clear total

Common File Labels/Synonyms
Total, Amount, Sum, Due, Balance, Grand Total, "Total:", "Amount Due:", "Payment:", "Charge:", or direct amounts without labels (e.g., "$125.50", "€89.99", "¥15,000")
5. Transaction Date
Definition: When the expense occurred or was processed.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Some form of date on document
Must be present somewhere
MOST LIKELY
Invoice date at document header
Standard date placement
MOST LIKELY
Transaction date on receipts
Point of sale timestamp
MOST LIKELY
"Date:" field near top of document
Labeled date field
POSSIBLE
Due date (not transaction date)
Payment deadline, not transaction date
POSSIBLE
Multiple dates (order, ship, invoice)
Various process dates

Common File Labels/Synonyms
Date, Invoice Date, Transaction Date, Purchase Date, "Date:", "Invoice Date:", "Transaction:", "Purchased:", or direct dates without labels (e.g., "2025-07-16", "July 16, 2025", "16/07/2025")
6. Invoice/Receipt Number
Definition: Unique identifier for the transaction.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Alphanumeric identifier on document
Usually present somewhere
MOST LIKELY
"Invoice #:", "Receipt #:" near header
Standard numbering labels
MOST LIKELY
Document number in top section
Header identification
MOST LIKELY
Reference number
Transaction reference
POSSIBLE
Order number (different from invoice)
Pre-invoice numbering
POSSIBLE
Confirmation codes
Alternative identifiers

Common File Labels/Synonyms
Invoice Number, Receipt Number, Reference, Order Number, "Invoice #:", "Receipt #:", "Ref:", "Order #:", or direct numbers without labels (e.g., "INV-2025-001", "12345", "RCP789")
7. Tax Information
Definition: Tax amounts and rates applied to the expense.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Tax amount if legally required
Jurisdiction-dependent requirement
Most Likely
Tax registration number for businesses
Legal business identifier
MOST LIKELY
"Tax:", "VAT:", "Sales Tax:" in summary
Standard tax labels
MOST LIKELY
Separate line items for tax
Itemized tax breakdown
POSSIBLE
Tax-inclusive pricing only
Total amount includes tax
POSSIBLE
Multiple tax types
Various tax categories

Common File Labels/Synonyms
Tax, VAT, Sales Tax, GST, HST, "Tax:", "VAT:", "Sales Tax:", "Tax Rate:", or direct tax amounts without labels (e.g., "$12.50 tax", "20% VAT", "HST: $5.25")
8. Payment Method
Definition: How the expense was paid.
How to Recognize on Files
Reliability
Location/Method
Description
MOST LIKELY
Credit card type and last 4 digits
Card payment details
MOST LIKELY
"Cash," "Card," "Check" indicators
Payment type labels
MOST LIKELY
Payment method in summary section
Payment information area
POSSIBLE
Bank transfer details
Electronic payment info
POSSIBLE
Digital payment indicators (PayPal, etc.)
Online payment methods

Common File Labels/Synonyms
Payment Method, Card Type, Paid By, "Payment:", "Card:", "Method:", "Paid:", or direct payment indicators without labels (e.g., "VISA ****1234", "Cash", "American Express")
9. Item Description/Line Items
Definition: Detailed breakdown of goods or services purchased.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Some description of what was purchased
Must be present in some form
MOST LIKELY
Itemized list in document body
Detailed product/service breakdown
MOST LIKELY
Product/service descriptions
Clear item identification
MOST LIKELY
Quantity and unit price details
Pricing breakdown
POSSIBLE
Summary descriptions only
General category information
POSSIBLE
Category codes instead of descriptions
Abbreviated item references

Common File Labels/Synonyms
Description, Item, Product, Service, Details, "Description:", "Item:", "Product:", "Service:", or direct product names without labels (e.g., "iPhone 17", "Office Chair", "Consulting Services")

