{"citations": {"supplier_name": {"field_citation": {"source_text": "Name of the supplier/vendor", "confidence": 0.8, "source_location": "requirements", "context": "\"field_type\": \"Supplier Name\", \"description\": \"Name of the supplier/vendor on invoice\"", "match_type": "contextual"}, "value_citation": {"source_text": "Austrian Airlines AG", "confidence": 0.95, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna", "match_type": "exact"}}, "supplier_address": {"field_citation": {"source_text": "Address of the supplier", "confidence": 0.8, "source_location": "requirements", "context": "\"field_type\": \"Supplier Address\", \"description\": \"Address of the supplier on invoice\"", "match_type": "contextual"}, "value_citation": {"source_text": "Office Park 2, A-1300 Vienna-Airport, Vienna, Austria", "confidence": 0.95, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}}, "vat_number": {"field_citation": {"source_text": "VAT Number", "confidence": 0.9, "source_location": "requirements", "context": "\"field_type\": \"VAT Number\", \"description\": \"VAT identification number\"", "match_type": "exact"}, "value_citation": {"source_text": "ATU15447707", "confidence": 0.95, "source_location": "markdown", "context": "VAT No. ATU15447707", "match_type": "exact"}}, "receipt_type": {"field_citation": {"source_text": "Passenger Receipt / Invoice", "confidence": 0.9, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "exact"}, "value_citation": {"source_text": "Passenger Receipt / Invoice", "confidence": 0.9, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "exact"}}, "personal_information": {"field_citation": {"source_text": "Name / Name:", "confidence": 0.85, "source_location": "markdown", "context": "Name / Name: FORISEK / MICHAL DR MR", "match_type": "contextual"}, "value_citation": {"source_text": "FORISEK / MICHAL DR MR", "confidence": 0.9, "source_location": "markdown", "context": "Name / Name: FORISEK / MICHAL DR MR", "match_type": "exact"}}, "transaction_reference": {"field_citation": {"source_text": "Receipt / Rechnung", "confidence": 0.8, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "fuzzy"}, "value_citation": {"source_text": "213000508057", "confidence": 0.95, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "exact"}}, "contact_name": {"field_citation": {"source_text": "Name / Name:", "confidence": 0.85, "source_location": "markdown", "context": "Name / Name: FORISEK / MICHAL DR MR", "match_type": "contextual"}, "value_citation": {"source_text": "<PERSON><PERSON>", "confidence": 0.9, "source_location": "markdown", "context": "Billing address / Rechnungsadresse: <PERSON><PERSON>", "match_type": "exact"}}, "contact_address": {"field_citation": {"source_text": "Billing address / Rechnungsadresse:", "confidence": 0.9, "source_location": "markdown", "context": "Billing address / Rechnungsadresse: <PERSON><PERSON>", "match_type": "exact"}, "value_citation": {"source_text": "Lubovnianska 14, 85107 Bratislava, Slovakia", "confidence": 0.95, "source_location": "markdown", "context": "Lubovnianska 14, 85107 Bratislava, Slovakia", "match_type": "exact"}}, "booking_code": {"field_citation": {"source_text": "Booking code", "confidence": 0.9, "source_location": "markdown", "context": "Booking code / Buchungscode: 6GHMCV", "match_type": "exact"}, "value_citation": {"source_text": "6GHMCV", "confidence": 0.95, "source_location": "markdown", "context": "Booking code / Buchungscode: 6GHMCV", "match_type": "exact"}}, "ticket_number": {"field_citation": {"source_text": "Ticket number", "confidence": 0.9, "source_location": "markdown", "context": "Ticket number / Ticketnummer: 257-2133783831", "match_type": "exact"}, "value_citation": {"source_text": "257-2133783831", "confidence": 0.95, "source_location": "markdown", "context": "Ticket number / Ticketnummer: 257-2133783831", "match_type": "exact"}}, "travel_date": {"field_citation": {"source_text": "DateDatum", "confidence": 0.9, "source_location": "markdown", "context": "| FlightFlug                                                                               | DateDatum | fromvon     | tonach", "match_type": "exact"}, "value_citation": {"source_text": "2013-08-31, 2013-09-08", "confidence": 0.95, "source_location": "markdown", "context": "| OS561 | 31 Aug 13 | Vienna Intl | Zurich\n| OS568 | 08 Sep 13 | Zurich | Vienna Intl", "match_type": "fuzzy"}}}, "metadata": {"total_fields_analyzed": 14, "fields_with_field_citations": 12, "fields_with_value_citations": 12, "average_confidence": 0.905}}