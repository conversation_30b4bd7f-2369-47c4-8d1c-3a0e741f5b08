{"citations": {"vat_number": {"field_citation": {"source_text": "VAT No.", "confidence": 0.9, "source_location": "markdown", "context": "Commercial Court Vienna, register no. 111000k, DVR 0091740, VAT No. ATU15447707", "match_type": "exact"}, "value_citation": {"source_text": "ATU15447707", "confidence": 0.9, "source_location": "markdown", "context": "Commercial Court Vienna, register no. 111000k, DVR 0091740, VAT No. ATU15447707", "match_type": "exact"}}, "receipt_type": {"field_citation": {"source_text": "Passenger Receipt / Invoice / Rechnung", "confidence": 0.9, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "exact"}, "value_citation": {"source_text": "Passenger Receipt / Invoice", "confidence": 0.8, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "exact"}}, "transaction_id": {"field_citation": {"source_text": "Receipt / Rechnung", "confidence": 0.7, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "contextual"}, "value_citation": {"source_text": "213000508057", "confidence": 0.9, "source_location": "markdown", "context": "# Passenger Receipt / Invoice / Rechnung    213000508057", "match_type": "exact"}}, "passenger_name": {"field_citation": {"source_text": "Name / Name", "confidence": 0.9, "source_location": "markdown", "context": "Name / Name: FORISEK / MICHAL DR MR", "match_type": "exact"}, "value_citation": {"source_text": "MICHAL DR MR FORISEK", "confidence": 0.9, "source_location": "markdown", "context": "Name / Name: FORISEK / MICHAL DR MR", "match_type": "fuzzy"}}, "booking_code": {"field_citation": {"source_text": "Booking code / Buchungscode", "confidence": 0.9, "source_location": "markdown", "context": "Booking code / Buchungscode: 6GHMCV", "match_type": "exact"}, "value_citation": {"source_text": "6GHMCV", "confidence": 0.9, "source_location": "markdown", "context": "Booking code / Buchungscode: 6GHMCV", "match_type": "exact"}}, "ticket_number": {"field_citation": {"source_text": "Ticket number / Ticketnummer", "confidence": 0.9, "source_location": "markdown", "context": "Ticket number / Ticketnummer: 257-2133783831", "match_type": "exact"}, "value_citation": {"source_text": "257-2133783831", "confidence": 0.9, "source_location": "markdown", "context": "Ticket number / Ticketnummer: 257-2133783831", "match_type": "exact"}}, "billing_address": {"field_citation": {"source_text": "Billing address / Rechnungsadresse", "confidence": 0.9, "source_location": "markdown", "context": "Billing address / Rechnungsadresse: <PERSON><PERSON>", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON>, Lubovnian<PERSON> 14, 85107 Bratislava, Slovakia", "confidence": 0.8, "source_location": "markdown", "context": "Billing address / Rechnungsadresse: <PERSON><PERSON>\n                                   Lubovnianska 14\n                                   85107 Bratislava\n                                   Slovakia", "match_type": "fuzzy"}}, "flight_data": {"field_citation": {"source_text": "Flight Data / Flugdaten", "confidence": 0.9, "source_location": "markdown", "context": "## Flight Data / Flugdaten", "match_type": "exact"}, "value_citation": {"source_text": "OS561, 2023-08-31, Vienna Intl, Zurich", "confidence": 0.8, "source_location": "markdown", "context": "| OS561                                                                                    | 31 Aug 13 | Vienna Intl | Zurich      |", "match_type": "fuzzy"}}, "operated_by": {"field_citation": {"source_text": "operated by", "confidence": 0.9, "source_location": "markdown", "context": "operated by: TYROLEAN AIRWAYS", "match_type": "exact"}, "value_citation": {"source_text": "TYROLEAN AIRWAYS", "confidence": 0.9, "source_location": "markdown", "context": "operated by: TYROLEAN AIRWAYS", "match_type": "exact"}}, "airline_name": {"field_citation": {"source_text": "Austrian Airlines AG", "confidence": 0.9, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}, "value_citation": {"source_text": "Austrian Airlines AG", "confidence": 0.9, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}}, "airline_office": {"field_citation": {"source_text": "registered office", "confidence": 0.9, "source_location": "markdown", "context": "registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}, "value_citation": {"source_text": "Vienna, Office Park 2, A-1300 Vienna-Airport", "confidence": 0.9, "source_location": "markdown", "context": "registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}}, "commercial_court_register_number": {"field_citation": {"source_text": "Commercial Court Vienna, register no.", "confidence": 0.9, "source_location": "markdown", "context": "Commercial Court Vienna, register no. 111000k", "match_type": "exact"}, "value_citation": {"source_text": "111000k", "confidence": 0.9, "source_location": "markdown", "context": "Commercial Court Vienna, register no. 111000k", "match_type": "exact"}}, "dvr_number": {"field_citation": {"source_text": "DVR", "confidence": 0.9, "source_location": "markdown", "context": "Commercial Court Vienna, register no. 111000k, DVR 0091740", "match_type": "exact"}, "value_citation": {"source_text": "0091740", "confidence": 0.9, "source_location": "markdown", "context": "Commercial Court Vienna, register no. 111000k, DVR 0091740", "match_type": "exact"}}}, "metadata": {"total_fields_analyzed": 16, "fields_with_field_citations": 13, "fields_with_value_citations": 13, "average_confidence": 0.8733333333333333}}