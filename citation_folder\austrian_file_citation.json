{"citations": {"supplier_name": {"field_citation": {"source_text": "Austrian Airlines AG", "confidence": 0.9, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}, "value_citation": {"source_text": "Austrian Airlines AG", "confidence": 0.9, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}}, "supplier_address": {"field_citation": {"source_text": "Office Park 2, A-1300 Vienna-Airport", "confidence": 0.8, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}, "value_citation": {"source_text": "Office Park 2, A-1300 Vienna-Airport", "confidence": 0.8, "source_location": "markdown", "context": "Austrian Airlines AG, Member of IATA, registered office: Vienna, Office Park 2, A-1300 Vienna-Airport", "match_type": "exact"}}, "vat_number": {"field_citation": {"source_text": "VAT no. ATU15447707", "confidence": 0.9, "source_location": "markdown", "context": "VAT No. ATU15447707", "match_type": "exact"}, "value_citation": {"source_text": "ATU15447707", "confidence": 0.9, "source_location": "markdown", "context": "VAT No. ATU15447707", "match_type": "exact"}}, "currency": {"field_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 0.6, "source_location": "requirements", "context": "Receipt currency", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "amount": {"field_citation": {"source_text": "Amount", "confidence": 0.6, "source_location": "requirements", "context": "Expense amount", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "receipt_type": {"field_citation": {"source_text": "Receipt / Invoice / Rechnung", "confidence": 0.9, "source_location": "markdown", "context": "Passenger Receipt / Invoice / Rechnung 213000508057", "match_type": "fuzzy"}, "value_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 0.9, "source_location": "markdown", "context": "Passenger Receipt / Invoice / Rechnung", "match_type": "fuzzy"}}, "receipt_quality": {"field_citation": {"source_text": "Receipt Quality", "confidence": 0.8, "source_location": "requirements", "context": "Document quality standard", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "personal_information": {"field_citation": {"source_text": "Personal Information", "confidence": 0.8, "source_location": "requirements", "context": "Privacy requirement for receipts", "match_type": "contextual"}, "value_citation": {"source_text": "Name / Name: FORISEK / MICHAL DR MR\nBilling address / Rechnungsadresse: <PERSON><PERSON>\nLubovnianska 14\n85107 Bratislava\nSlovakia", "confidence": 0.95, "source_location": "markdown", "context": "Name / Name: FORISEK / MICHAL DR MR\nBilling address / Rechnungsadresse: <PERSON><PERSON>\nLubovnianska 14\n85107 Bratislava\nSlovakia", "match_type": "exact"}}, "business_trip_reporting": {"field_citation": {"source_text": "Business Trip Reporting", "confidence": 0.8, "source_location": "requirements", "context": "Separate reports requirement", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "travel_template": {"field_citation": {"source_text": "Travel Template", "confidence": 0.8, "source_location": "requirements", "context": "Specific reporting template", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "manager_approval": {"field_citation": {"source_text": "Manager <PERSON><PERSON><PERSON><PERSON>", "confidence": 0.8, "source_location": "requirements", "context": "Direct manager approval", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "route_map": {"field_citation": {"source_text": "Route Map", "confidence": 0.8, "source_location": "requirements", "context": "Travel route documentation", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "kilometer_record": {"field_citation": {"source_text": "Kilometer Record", "confidence": 0.8, "source_location": "requirements", "context": "Distance traveled documentation", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "car_details": {"field_citation": {"source_text": "Car Details", "confidence": 0.8, "source_location": "requirements", "context": "Vehicle information", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "parking_documentation": {"field_citation": {"source_text": "Parking Documentation", "confidence": 0.8, "source_location": "requirements", "context": "Parking expense documentation", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "contact_phone": {"field_citation": {"source_text": "Contact Phone", "confidence": 0.7, "source_location": "requirements", "context": "Not explicitly mentioned in the provided requirements or document.", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "contact_email": {"field_citation": {"source_text": "Contact Email", "confidence": 0.7, "source_location": "requirements", "context": "Not explicitly mentioned in the provided requirements or document.", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "contact_website": {"field_citation": {"source_text": "Contact Website", "confidence": 0.7, "source_location": "requirements", "context": "Not explicitly mentioned in the provided requirements or document.", "match_type": "contextual"}, "value_citation": {"source_text": null, "confidence": 0, "source_location": "", "context": "", "match_type": ""}}, "transaction_reference": {"field_citation": {"source_text": "Invoice / Rechnung 213000508057", "confidence": 0.9, "source_location": "markdown", "context": "Passenger Receipt / Invoice / Rechnung 213000508057", "match_type": "fuzzy"}, "value_citation": {"source_text": "213000508057", "confidence": 0.9, "source_location": "markdown", "context": "Passenger Receipt / Invoice / Rechnung 213000508057", "match_type": "fuzzy"}}}, "metadata": {"total_fields_analyzed": 22, "fields_with_field_citations": 14, "fields_with_value_citations": 5, "average_confidence": 0.76}}