#!/usr/bin/env python3
"""
Test script for separated validation system
"""

import asyncio
import os
import pathlib
import sys
import shutil
from dotenv import load_dotenv
from agno.utils.log import logger
from agno.storage.sqlite import SqliteStorage

from expense_processing_workflow import ExpenseProcessingWorkflow

# Load environment variables
load_dotenv()

async def test_main_workflow():
    """Test the main workflow without validation."""
    
    # Test configuration
    TEST_DATASET_DIR = "test_dataset"
    TEST_INPUT_FOLDER = "test_expense_files"
    LLAMAPARSE_API_KEY = os.getenv("LLAMAPARSE_API_KEY")
    
    logger.info("🧪 Testing main workflow without validation")
    logger.info(f"Dataset directory: {TEST_DATASET_DIR}")
    logger.info(f"Input folder: {TEST_INPUT_FOLDER}")
    
    try:
        workflow = ExpenseProcessingWorkflow(
            session_id=f"test-expense-processing-{TEST_DATASET_DIR}",
            storage=SqliteStorage(
                table_name="test_expense_processing_workflows",
                db_file="test_expense_processing.db"
            ),
            debug_mode=True
        )

        async for response in workflow.process_expenses(
            dataset_dir=TEST_DATASET_DIR,
            llamaparse_api_key=LLAMAPARSE_API_KEY,
            input_folder=TEST_INPUT_FOLDER
        ):
            logger.info(f"Workflow update: {response.content}")

        summary = workflow.session_state.get("summary", "No summary available")
        logger.info(f"✅ Main workflow completed. {summary}")
        logger.info("Individual results saved to results/ directory")
        
        return True

    except Exception as e:
        logger.error(f"❌ Main workflow failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_standalone_validation():
    """Test the standalone validation runner."""
    
    logger.info("🧪 Testing standalone validation runner")
    
    try:
        from standalone_validation_runner import StandaloneValidationRunner
        
        # Initialize validation runner
        runner = StandaloneValidationRunner(
            results_dir="results",
            quality_dir="llm_quality_reports",
            validation_output_dir="test_validation_results"
        )
        
        # Run validation
        summary = await runner.run_validation(
            validate_compliance=True,
            validate_quality=True
        )
        
        logger.info("✅ Standalone validation completed")
        logger.info(f"   Total files processed: {summary['total_files_processed']}")
        logger.info(f"   Total validation time: {summary['total_validation_time']} seconds")
        
        if summary['compliance_validation']['enabled']:
            compliance_count = len(summary['compliance_validation']['results'])
            logger.info(f"   Compliance validations: {compliance_count}")
        
        if summary['quality_validation']['enabled']:
            quality_count = len(summary['quality_validation']['results'])
            logger.info(f"   Quality validations: {quality_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Standalone validation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """Clean up test files."""
    test_dirs = [
        "test_expense_files",
        "test_dataset", 
        "test_validation_results",
        "test_expense_processing.db"
    ]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            if os.path.isdir(test_dir):
                shutil.rmtree(test_dir)
            else:
                os.remove(test_dir)
            logger.info(f"🧹 Cleaned up: {test_dir}")

async def main():
    """Main test function."""
    
    logger.info("🚀 Starting separated validation system test")
    
    # Validate environment
    if not os.getenv("OPENAI_API_KEY"):
        logger.error("❌ Missing OPENAI_API_KEY environment variable")
        return 1
    
    if not os.getenv("ANTHROPIC_API_KEY"):
        logger.error("❌ Missing ANTHROPIC_API_KEY environment variable")
        return 1
    
    if not os.getenv("LLAMAPARSE_API_KEY"):
        logger.error("❌ Missing LLAMAPARSE_API_KEY environment variable")
        return 1
    
    try:
        # Test 1: Run main workflow without validation
        logger.info("\n" + "="*50)
        logger.info("TEST 1: Main Workflow (without validation)")
        logger.info("="*50)
        
        workflow_success = await test_main_workflow()
        if not workflow_success:
            logger.error("❌ Main workflow test failed")
            return 1
        
        # Test 2: Run standalone validation
        logger.info("\n" + "="*50)
        logger.info("TEST 2: Standalone Validation")
        logger.info("="*50)
        
        validation_success = await test_standalone_validation()
        if not validation_success:
            logger.error("❌ Standalone validation test failed")
            return 1
        
        logger.info("\n" + "="*50)
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("✅ Main workflow runs without validation")
        logger.info("✅ Standalone validation works on outputs")
        logger.info("="*50)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Clean up test files
        cleanup_test_files()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
