#!/usr/bin/env python3
"""
Test script to verify schema-based taxonomy replacement in issue detection agent
"""

import json
from issue_detection_agent import format_expense_taxonomy, EXPENSE_SCHEMA

def test_schema_taxonomy_replacement():
    """Test that the schema-based taxonomy replacement works correctly."""
    
    print("🧪 Testing Schema-Based Taxonomy Replacement")
    print("=" * 60)
    
    try:
        # Test 1: Check if schema loads correctly
        print("📋 Test 1: Schema Loading")
        print(f"   Schema title: {EXPENSE_SCHEMA.get('title', 'N/A')}")
        print(f"   Schema description: {EXPENSE_SCHEMA.get('description', 'N/A')}")
        print(f"   Number of properties: {len(EXPENSE_SCHEMA.get('properties', {}))}")
        
        properties = EXPENSE_SCHEMA.get('properties', {})
        print(f"   Field names: {list(properties.keys())}")
        print("   ✅ Schema loaded successfully")
        
        # Test 2: Check taxonomy formatting
        print(f"\n📝 Test 2: Taxonomy Formatting")
        taxonomy_text = format_expense_taxonomy()
        
        print(f"   Generated taxonomy length: {len(taxonomy_text)} characters")
        print(f"   Starts with: {taxonomy_text[:50]}...")
        
        # Check if all fields are included
        field_count = 0
        for field_name, field_info in properties.items():
            title = field_info.get('title', field_name)
            if title in taxonomy_text:
                field_count += 1
        
        print(f"   Fields found in taxonomy: {field_count}/{len(properties)}")
        
        if field_count == len(properties):
            print("   ✅ All schema fields included in taxonomy")
        else:
            print("   ❌ Some schema fields missing from taxonomy")
            return False
        
        # Test 3: Check content structure
        print(f"\n🔍 Test 3: Content Structure")
        lines = taxonomy_text.split('\n')
        
        # Should start with taxonomy header
        if lines[0].strip() == "#Expense Management System Taxonomy":
            print("   ✅ Correct header format")
        else:
            print(f"   ❌ Incorrect header: {lines[0]}")
            return False
        
        # Should contain numbered sections
        numbered_sections = [line for line in lines if line.strip() and line[0].isdigit()]
        print(f"   Numbered sections found: {len(numbered_sections)}")
        
        if len(numbered_sections) == len(properties):
            print("   ✅ Correct number of sections")
        else:
            print(f"   ❌ Expected {len(properties)} sections, found {len(numbered_sections)}")
            return False
        
        # Test 4: Compare with original taxonomy content
        print(f"\n📊 Test 4: Content Comparison")
        
        # Check for key taxonomy elements
        key_elements = [
            "Supplier (Service Provider)",
            "Consumer (Recipient)", 
            "ICP (Independent Contractor Program) Requirements",
            "Transaction Amount",
            "Transaction Date",
            "Invoice/Receipt Number",
            "Tax Information",
            "Payment Method",
            "Item Description/Line Items"
        ]
        
        elements_found = 0
        for element in key_elements:
            if element in taxonomy_text:
                elements_found += 1
        
        print(f"   Key elements found: {elements_found}/{len(key_elements)}")
        
        if elements_found == len(key_elements):
            print("   ✅ All key taxonomy elements present")
        else:
            print("   ❌ Some key elements missing")
            return False
        
        # Test 5: Show sample output
        print(f"\n📄 Test 5: Sample Output")
        print("   First 500 characters of generated taxonomy:")
        print("   " + "-" * 50)
        print("   " + taxonomy_text[:500].replace('\n', '\n   '))
        print("   " + "-" * 50)
        
        print(f"\n🎯 Test Result: ✅ ALL TESTS PASSED!")
        print("🎉 Schema-based taxonomy replacement is working correctly!")
        print("   - Schema loads successfully")
        print("   - All fields are included in taxonomy")
        print("   - Content structure is correct")
        print("   - All key elements are present")
        print("   - Generated taxonomy matches expected format")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    
    try:
        success = test_schema_taxonomy_replacement()
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
