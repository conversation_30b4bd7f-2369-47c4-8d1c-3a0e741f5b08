#!/usr/bin/env python3
"""
Test script to verify validation format transformation works correctly
"""

import json
from datetime import datetime

def test_validation_format_transformation():
    """Test that the transformation produces the expected format."""
    
    print("🧪 Testing Validation Format Transformation")
    print("=" * 60)
    
    # Mock raw UQLM output (what llm_output_checker returns)
    raw_uqlm_output = {
        "validation_summary": {
            "overall_confidence": 0.7325,
            "is_reliable": True,
            "reliability_level": "MEDIUM",
            "critical_issues": [
                "Issue 1: Test issue",
                "Issue 2: Another test issue"
            ],
            "recommendation": "AI response is generally reliable but review flagged issues before using.",
            "validated_issues_count": 5,
            "ai_confidence": 0.0
        },
        "dimensional_analysis": {
            "factual_grounding": {
                "confidence_score": 0.85,
                "reliability_level": "high",
                "summary": "Test summary for factual grounding",
                "issues": ["Test issue 1"],
                "raw_response": "Test raw response"
            },
            "knowledge_base_adherence": {
                "confidence_score": 0.6,
                "reliability_level": "medium", 
                "summary": "Test summary for knowledge base",
                "issues": ["Test issue 2", "Test issue 3"],
                "raw_response": "Test raw response 2"
            }
        },
        "compliance_metadata": {
            "country": "Austria",
            "receipt_type": "flights",
            "icp": "Global People",
            "validation_method": "UQLM LLMPanel",
            "panel_judges": 2,
            "original_issues_found": 5
        }
    }
    
    # Mock the transformation function (simplified version)
    def create_readable_validation_report(validation_data):
        """Create a readable validation report similar to integrated workflow format."""
        if not validation_data:
            return {"error": "No validation data available"}

        summary = validation_data.get('validation_summary', {})
        dimensional = validation_data.get('dimensional_analysis', {})
        metadata = validation_data.get('compliance_metadata', {})

        report = {
            "validation_report": {
                "timestamp": datetime.now().isoformat(),
                "overall_assessment": {
                    "confidence_score": summary.get('overall_confidence', 0),
                    "reliability_level": summary.get('reliability_level', 'UNKNOWN'),
                    "is_reliable": summary.get('is_reliable', False),
                    "recommendation": summary.get('recommendation', 'No recommendation available')
                },
                "critical_issues_summary": {
                    "total_issues": len(summary.get('critical_issues', [])),
                    "issues": summary.get('critical_issues', [])
                },
                "dimensional_analysis_summary": {}
            },
            "detailed_analysis": {
                "metadata": {
                    "country": metadata.get('country', 'Unknown'),
                    "receipt_type": metadata.get('receipt_type', 'Unknown'),
                    "icp": metadata.get('icp', 'Unknown'),
                    "validation_method": metadata.get('validation_method', 'UQLM'),
                    "panel_judges": metadata.get('panel_judges', 0),
                    "original_issues_found": metadata.get('original_issues_found', 0)
                },
                "dimension_details": {}
            }
        }

        # Add dimensional analysis in readable format
        for dimension, result in dimensional.items():
            if isinstance(result, dict):
                dimension_data = {
                    "confidence_score": result.get('confidence_score', 0),
                    "reliability_level": result.get('reliability_level', 'unknown'),
                    "summary": result.get('summary', 'No summary available'),
                    "issues_found": result.get('issues', []),
                    "total_issues": len(result.get('issues', []))
                }
            else:
                dimension_data = str(result)

            # Add to summary
            report["validation_report"]["dimensional_analysis_summary"][dimension] = {
                "confidence": dimension_data.get("confidence_score", 0) if isinstance(dimension_data, dict) else "N/A",
                "reliability": dimension_data.get("reliability_level", "unknown") if isinstance(dimension_data, dict) else "N/A",
                "issues_count": dimension_data.get("total_issues", 0) if isinstance(dimension_data, dict) else 0
            }

            # Add to detailed analysis
            report["detailed_analysis"]["dimension_details"][dimension] = dimension_data

        return report
    
    # Test the transformation
    print("📋 Test 1: Raw UQLM Output Structure")
    print(f"   Input format keys: {list(raw_uqlm_output.keys())}")
    print(f"   Validation summary keys: {list(raw_uqlm_output['validation_summary'].keys())}")
    print(f"   Dimensional analysis keys: {list(raw_uqlm_output['dimensional_analysis'].keys())}")
    
    print("\n📋 Test 2: Transformation Process")
    try:
        transformed_output = create_readable_validation_report(raw_uqlm_output)
        print("   ✅ Transformation completed successfully")
        
        # Check expected structure
        expected_keys = ["validation_report", "detailed_analysis"]
        for key in expected_keys:
            if key in transformed_output:
                print(f"   ✅ Has '{key}' section")
            else:
                print(f"   ❌ Missing '{key}' section")
                return False
                
    except Exception as e:
        print(f"   ❌ Transformation failed: {str(e)}")
        return False
    
    print("\n📋 Test 3: Expected Format Structure")
    validation_report = transformed_output.get("validation_report", {})
    detailed_analysis = transformed_output.get("detailed_analysis", {})
    
    # Check validation_report structure
    vr_keys = ["timestamp", "overall_assessment", "critical_issues_summary", "dimensional_analysis_summary"]
    for key in vr_keys:
        if key in validation_report:
            print(f"   ✅ validation_report has '{key}'")
        else:
            print(f"   ❌ validation_report missing '{key}'")
            return False
    
    # Check detailed_analysis structure
    da_keys = ["metadata", "dimension_details"]
    for key in da_keys:
        if key in detailed_analysis:
            print(f"   ✅ detailed_analysis has '{key}'")
        else:
            print(f"   ❌ detailed_analysis missing '{key}'")
            return False
    
    print("\n📋 Test 4: Data Preservation")
    overall_assessment = validation_report.get("overall_assessment", {})
    
    # Check confidence score
    if overall_assessment.get("confidence_score") == 0.7325:
        print("   ✅ Confidence score preserved")
    else:
        print(f"   ❌ Confidence score not preserved: {overall_assessment.get('confidence_score')}")
        return False
    
    # Check critical issues
    critical_issues = validation_report.get("critical_issues_summary", {})
    if critical_issues.get("total_issues") == 2:
        print("   ✅ Critical issues count preserved")
    else:
        print(f"   ❌ Critical issues count not preserved: {critical_issues.get('total_issues')}")
        return False
    
    # Check dimensional analysis
    dimension_details = detailed_analysis.get("dimension_details", {})
    if "factual_grounding" in dimension_details:
        fg_data = dimension_details["factual_grounding"]
        if fg_data.get("confidence_score") == 0.85:
            print("   ✅ Dimensional analysis data preserved")
        else:
            print(f"   ❌ Dimensional analysis confidence not preserved: {fg_data.get('confidence_score')}")
            return False
    else:
        print("   ❌ Factual grounding dimension missing")
        return False
    
    print("\n📋 Test 5: JSON Serialization")
    try:
        json_str = json.dumps(transformed_output, indent=2)
        print(f"   ✅ JSON serialization successful ({len(json_str)} characters)")
        
        # Verify it can be parsed back
        parsed_back = json.loads(json_str)
        if parsed_back["validation_report"]["overall_assessment"]["confidence_score"] == 0.7325:
            print("   ✅ Round-trip serialization successful")
        else:
            print("   ❌ Round-trip serialization failed")
            return False
            
    except Exception as e:
        print(f"   ❌ JSON serialization failed: {str(e)}")
        return False
    
    print("\n🎯 Test Result: ✅ ALL TESTS PASSED!")
    print("🎉 Validation format transformation is working correctly!")
    print("   - Raw UQLM output transformed to expected format")
    print("   - All required sections present")
    print("   - Data preservation verified")
    print("   - JSON serialization successful")
    print("   - Format matches integrated workflow output")
    
    return True

def main():
    """Run the test."""
    
    try:
        success = test_validation_format_transformation()
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
