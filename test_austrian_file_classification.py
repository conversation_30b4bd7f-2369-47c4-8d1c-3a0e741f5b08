#!/usr/bin/env python3
"""
Test script for schema-based file classification with austrian_file
"""

import json
from file_classification_agent import classify_file

def test_austrian_file():
    """Test with the actual austrian_file content."""
    
    # Load the austrian_file content
    with open('llamaparse_output/austrian_file.md', 'r', encoding='utf-8') as f:
        austrian_file_content = f.read()
    
    print("🧪 Testing Schema-Based Classification with Austrian File:")
    print("=" * 60)
    print("File Content Preview:")
    print(austrian_file_content[:500] + "..." if len(austrian_file_content) > 500 else austrian_file_content)
    print("=" * 60)
    
    # Run classification
    result = classify_file(austrian_file_content, "Austria")
    
    # Extract content
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)
    
    print("Raw Classification Response:")
    print("-" * 40)
    print(content)
    print("-" * 40)
    
    try:
        parsed_result = json.loads(content)
        
        print("\n✅ PARSED CLASSIFICATION RESULT:")
        print(f"   📄 Is Expense: {parsed_result.get('is_expense')}")
        print(f"   🏷️  Expense Type: {parsed_result.get('expense_type')}")
        print(f"   🌍 Language: {parsed_result.get('language')} (confidence: {parsed_result.get('language_confidence')}%)")
        print(f"   📍 Document Location: {parsed_result.get('document_location')}")
        print(f"   📍 Expected Location: {parsed_result.get('expected_location')}")
        print(f"   ✅ Location Match: {parsed_result.get('location_match')}")
        
        # Schema field analysis
        schema_analysis = parsed_result.get('schema_field_analysis', {})
        if schema_analysis:
            print(f"\n🔍 SCHEMA FIELD ANALYSIS:")
            print(f"   ✅ Fields Found: {schema_analysis.get('fields_found', [])}")
            print(f"   ❌ Fields Missing: {schema_analysis.get('fields_missing', [])}")
            print(f"   📊 Total Fields Found: {schema_analysis.get('total_fields_found', 0)}/8")
            print(f"   💭 Reasoning: {schema_analysis.get('expense_identification_reasoning', 'N/A')}")
        
        # Overall reasoning
        print(f"\n💭 OVERALL REASONING:")
        print(f"   {parsed_result.get('reasoning', 'N/A')}")
        
        # Validation
        is_expense = parsed_result.get('is_expense', False)
        fields_found = schema_analysis.get('total_fields_found', 0)
        
        print(f"\n📋 VALIDATION:")
        print(f"   Expected: This should be an expense (flight ticket)")
        print(f"   Result: {'✅ CORRECT' if is_expense else '❌ INCORRECT'}")
        print(f"   Fields Found: {fields_found} (should be 3+ for expense)")
        
        return is_expense and fields_found >= 3
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        print(f"Raw content: {content}")
        return False

def main():
    """Run the austrian file classification test."""
    
    print("🚀 Testing Simplified Schema-Based File Classification")
    print("🎯 Target: Austrian Airlines Flight Receipt")
    print("=" * 70)
    
    try:
        test_passed = test_austrian_file()
        
        print("\n" + "=" * 70)
        print("📊 TEST SUMMARY:")
        print(f"   Austrian File Classification: {'✅ PASSED' if test_passed else '❌ FAILED'}")
        
        if test_passed:
            print("\n🎉 SUCCESS! Schema-based classification correctly identified the Austrian flight receipt as an expense.")
            return 0
        else:
            print("\n❌ FAILED! The classification did not work as expected.")
            return 1
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
