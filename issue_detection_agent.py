from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.utils.log import logger
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from llm_output_checker import ExpenseComplianceUQLMValidator
from textwrap import dedent
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

llm_client = ChatAnthropic(model="claude-sonnet-4-20250514",
                        api_key=os.getenv("ANTHROPIC_API_KEY"))

# Create the issue detection and analysis agent
issue_detection_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    #model=<PERSON>(id="claude-3-7-sonnet-20250219"),
    instructions=dedent("""\
Persona: You are an expert compliance and tax analysis AI specializing in expense document validation. Your primary function is to analyze extracted receipt data against country-specific compliance requirements and ICP-specific rules to identify issues, violations, and recommendations.

Task: Perform comprehensive issue detection and analysis by cross-referencing extracted receipt data against the provided country database and ICP-specific requirements.

ANALYSIS WORKFLOW:
1. Load and understand the compliance requirements from the country database
2. Analyze the extracted receipt data against these requirements
3. Identify specific compliance violations, tax implications, and documentation gaps
4. Categorize each issue according to the specified categories
5. Provide specific recommendations based on the knowledge base

ISSUE CATEGORIES:

CATEGORY 1: COMPLIANCE VIOLATIONS REQUIRING FIXES
Issue type: Standards & Compliance | Fix Identified
Flag issue type: Standards & Compliance related
Scope: Mandatory field violations, format errors, missing required information
Examples:
- "The VAT number has only 2 numbers, should have 9"
- "Missing mandatory supplier name on the receipt"
- "Invoice number is not clearly visible or missing"
- "Date of issue is not present on the receipt"
- "Required supplier address is missing or incomplete"
- "Local employer details are not present on the invoice"
- "Receipt currency does not match required local currency"
- "Missing required VAT identification number"
- "Invoice serial number missing for invoices over threshold amount"
- "Net amount, tax rate, or VAT amount missing for high-value invoices"
- "Worker name and address missing for required invoice types"
- "Supplier tax ID missing for invoices above specified threshold"
- "Receipt quality is poor, not meeting clear and readable standards"
Recommendation: "It is recommended to address this issue with the supplier or provider"


CATEGORY 2: TAX IMPLICATIONS AND GROSS-UP SCENARIOS
Issue type: Standards & Compliance | Gross-up Identified
Flag issue type: Standards & Compliance related
Scope: Expense limits, tax exemption violations, gross-up requirements
Examples:
- "Phone expenses in this country is limited to €20/month"
"Home office expenses exceed the maximum of €1,260/year"
"Wellness benefits exceed the maximum of €600/year"
"Meal expenses are not tax exempt and will be grossed up"
"Fuel expenses will be taxed as per country regulations"
"Entertainment expenses without third party involvement are not tax exempt"
"Transportation to workplace expenses are not tax exempt"
"Personal meal expenses during non-business travel are taxable"
"Office groceries expenses are not tax exempt"
"Internet expenses exceed the flat rate tax-free allowance"
"Mobile phone expenses without separate personal phone proof are taxable"
Recommendation: State the specific gross-up guidelines for this type of expense based on the knowledge base (e.g., "Phone expenses are tax-free up to €20/month, amounts exceeding this limit will be grossed-up" or "Home office expenses are tax exempt up to €6/day, maximum €1,260/year, excess amounts will be taxed")


CATEGORY 3: ADDITIONAL DOCUMENTATION REQUIREMENTS
Issue type: Standards & Compliance | Follow-up Action Identified
Flag issue type: Standards & Compliance related
Scope: Missing supporting documentation, approval requirements, additional forms
Examples:
- "Expense is car rental related - additional documentation is required"
- "Mileage claim requires logbook with date, route, purpose, and odometer readings"
- "Training expenses require direct manager approval"
- "Flight expenses require A1 certificate when traveling"
- "Mobile phone expenses require proof of separate personal phone"
- "IT equipment expenses require company property documentation"
- "Entertainment expenses require proof of third party involvement"
- "Travel expenses require specific travel expense report template"
- "International travel requires per diem calculation and additional documentation"
- "Invoices over threshold amount require additional detailed information"
- "Business travel expenses require route details and Google Maps documentation"
- "Phone expenses require invoice to include company name in c/o format"
- "Office supplies require invoice with company name and details"
- "Internet expenses require proper documentation and company details on invoice"
- "Original invoices must be kept for required storage period (e.g., 10 years)"
Recommendation examples:
- "Submission of car rental expense in this country requires, in addition the mileage breakdown from the car rental service, per day"
- "Please provide mileage logbook with complete route details and odometer readings"
- "Manager approval is required before processing this training expense"
- "Please provide A1 certificate for international travel documentation"
- "Please provide proof of separate personal phone for mobile phone reimbursement"
- "Please use the specific travel expense report template for this country"
- "Please provide map with route details (Google Maps sufficient) for mileage claims"
- "Please ensure invoice includes company name and VAT details as required"


CRITICAL REQUIREMENTS:
- ONLY use knowledge from the provided country database and ICP-specific rules
- DO NOT make up any information not provided in the knowledge base
- Cross-reference ALL extracted data fields against specific country and ICP requirements
- Quote the knowledge base when providing issues and recommendations
- Ensure all analysis is based on the provided compliance standards and policies
- Be thorough and systematic in checking every applicable requirement
- Dynamically filter requirements based on ICP, receipt type, and expense category
- Calculate confidence score based on clarity of violations and knowledge base coverage
- Your output MUST BE ONLY a valid JSON object matching the specified structure

OUTPUT FORMAT:
Return a JSON object with the following structure:

{
  "validation_result": {
    "is_valid": true/false,
    "issues_count": number,
    "issues": [
      {
        "issue_type": "Standards & Compliance | Fix Identified/Gross-up Identified/Follow-up Action Identified",
        "field": "specific_field_name",
        "description": "Detailed description of the issue based on knowledge base",
        "recommendation": "Specific action to resolve based on compliance requirements",
        "knowledge_base_reference": "Quote from the compliance data that supports this finding"
      }
    ],
    "corrected_receipt": null,
    "compliance_summary": "Overall compliance assessment and key findings"
  },
  "technical_details": {
    "content_type": "ReceiptValidationResult",
    "country": "analyzed_country",
    "icp": "analyzed_icp",
    "receipt_type": "analyzed_receipt_type",
    "issues_count": number_of_issues,
    "has_reasoning": true
  }
}

VALIDATION CHECKLIST:
□ Check all mandatory fields against FileRelatedRequirements
□ Validate expense type against ExpenseTypes rules
□ Check ICP-specific requirements and rules
□ Verify tax exemption limits and gross-up scenarios
□ Identify missing documentation requirements
□ Cross-reference location-specific compliance rules
□ Validate currency and amount formatting
□ Check storage and retention requirements"""),
    reasoning=True,
    markdown=False,
    show_tool_calls=False
)

async def analyze_compliance_issues(country: str, receipt_type: str, icp: str, compliance_json: dict, extracted_json: dict) -> str:
    """
    Analyze extracted receipt data against compliance requirements to detect issues.
    
    Args:
        country: Country for compliance rules (e.g., "Germany")
        receipt_type: Type of receipt (e.g., "All", "Travel", "Mileage", etc.)
        icp: ICP name (e.g., "Global People", "goGlobal", "Parakar", "Atlas")
        compliance_json: Country-specific compliance requirements
        extracted_json: Extracted data from the receipt
        
    Returns:
        JSON string with detailed issue analysis
    """
    # Format the prompt with all required data
    formatted_prompt = f"""COMPLIANCE ANALYSIS REQUEST:

COUNTRY: {country}
RECEIPT TYPE: {receipt_type}
ICP: {icp}

COMPLIANCE REQUIREMENTS (Country Database):
{json.dumps(compliance_json, indent=2)}

EXTRACTED RECEIPT DATA:
{json.dumps(extracted_json, indent=2)}

ANALYSIS INSTRUCTIONS:
Perform comprehensive compliance analysis by:
1. Cross-referencing each extracted field against the FileRelatedRequirements for the specified ICP and receipt type
2. Checking expense type against ExpenseTypes rules and limits
3. Identifying any missing mandatory fields or incorrect formats
4. Detecting tax implications and gross-up scenarios
5. Identifying additional documentation requirements
6. Providing specific recommendations based on the knowledge base

Analyze systematically and provide detailed findings in the specified format.

#Expense Management System Taxonomy
Here is a taxonomy to main data fields that will appear on an expense file (or invoice, receipt). Please note these are not the only fields that can be expected, there can be more. 
1. Supplier (Service Provider)
Definition: Entity that provides goods, services, or products in exchange for payment.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Payment recipient name somewhere
Business identifier (name, logo, or tax ID) must appear
MOST LIKELY
Company name at top of document
Header section with business logo or letterhead
MOST LIKELY
"Bill From" or "Sold By" sections
Dedicated supplier information areas
MOST LIKELY
Merchant name on credit card statements
Transaction details from payment processor
POSSIBLE
Footer information
Contact details or business registration
POSSIBLE
Payment details section
Banking or remittance information

Common File Labels/Synonyms
Vendor, Merchant, Seller, Provider, Company, "From:", "Billed By:", "Sold By:", "Service Provider:", Business Name, Organization, Entity, Contractor, or direct company names without labels (e.g., "Apple Inc.", "Delta Airlines", "Starbucks")
2. Consumer (Recipient)
Definition: Person, department, or entity that receives goods/services and is responsible for the expense.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Some form of customer/buyer identification
Must be identifiable somewhere on document
MOST LIKELY
"Bill To" or "Customer" information
Dedicated customer details section
MOST LIKELY
Employee name on receipts
Individual identification on transaction
MOST LIKELY
Cardholder name on statements
Payment method owner information
POSSIBLE
"Purchased By" or "Ordered By" sections
Alternative customer identification
POSSIBLE
Department codes or cost centers
Organizational unit identification

Common File Labels/Synonyms
Customer, Client, Buyer, Purchaser, Employee, "Bill To:", "Customer:", "Ordered By:", "Cardholder:", Department, Cost Center, Team, Individual, or direct names without labels (e.g., "John Smith", "Marketing Department", "ABC Corp")
3. ICP (Independent Contractor Program) Requirements
Definition: ICP is the local employer for EOR (Employer of Record) employees.
Critical Rule: If the local ICP REQUIRES that their details be listed on the expense files, then all required consumer details on the expense record must be the ICP details (not the individual employee details).
How to Recognize ICP Requirements on Files
Reliability
Location/Method
Description
Most Likely
ICP company name as "Bill To" or "Customer"
Local employer entity listed as consumer
Most Likely
ICP business registration details as consumer
Official business entity information
MOST LIKELY
ICP address/contact info in consumer section
Local company billing address
MOST LIKELY
Corporate billing address in consumer section
Business entity rather than individual
POSSIBLE
Mixed billing scenarios
Regional variations in ICP requirements

4. Transaction Amount
Definition: The monetary value of the expense.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Total amount somewhere on document
Must include currency denomination
MOST LIKELY
"Total:", "Amount Due:", "Grand Total:"
Standard amount labels
MOST LIKELY
Bottom right of invoice/receipt
Common amount placement
MOST LIKELY
Summary section
Calculation breakdown area
POSSIBLE
Line item totals only
Multiple amounts without clear total

Common File Labels/Synonyms
Total, Amount, Sum, Due, Balance, Grand Total, "Total:", "Amount Due:", "Payment:", "Charge:", or direct amounts without labels (e.g., "$125.50", "€89.99", "¥15,000")
5. Transaction Date
Definition: When the expense occurred or was processed.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Some form of date on document
Must be present somewhere
MOST LIKELY
Invoice date at document header
Standard date placement
MOST LIKELY
Transaction date on receipts
Point of sale timestamp
MOST LIKELY
"Date:" field near top of document
Labeled date field
POSSIBLE
Due date (not transaction date)
Payment deadline, not transaction date
POSSIBLE
Multiple dates (order, ship, invoice)
Various process dates

Common File Labels/Synonyms
Date, Invoice Date, Transaction Date, Purchase Date, "Date:", "Invoice Date:", "Transaction:", "Purchased:", or direct dates without labels (e.g., "2025-07-16", "July 16, 2025", "16/07/2025")
6. Invoice/Receipt Number
Definition: Unique identifier for the transaction.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Alphanumeric identifier on document
Usually present somewhere
MOST LIKELY
"Invoice #:", "Receipt #:" near header
Standard numbering labels
MOST LIKELY
Document number in top section
Header identification
MOST LIKELY
Reference number
Transaction reference
POSSIBLE
Order number (different from invoice)
Pre-invoice numbering
POSSIBLE
Confirmation codes
Alternative identifiers

Common File Labels/Synonyms
Invoice Number, Receipt Number, Reference, Order Number, "Invoice #:", "Receipt #:", "Ref:", "Order #:", or direct numbers without labels (e.g., "INV-2025-001", "12345", "RCP789")
7. Tax Information
Definition: Tax amounts and rates applied to the expense.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Tax amount if legally required
Jurisdiction-dependent requirement
Most Likely
Tax registration number for businesses
Legal business identifier
MOST LIKELY
"Tax:", "VAT:", "Sales Tax:" in summary
Standard tax labels
MOST LIKELY
Separate line items for tax
Itemized tax breakdown
POSSIBLE
Tax-inclusive pricing only
Total amount includes tax
POSSIBLE
Multiple tax types
Various tax categories

Common File Labels/Synonyms
Tax, VAT, Sales Tax, GST, HST, "Tax:", "VAT:", "Sales Tax:", "Tax Rate:", or direct tax amounts without labels (e.g., "$12.50 tax", "20% VAT", "HST: $5.25")
8. Payment Method
Definition: How the expense was paid.
How to Recognize on Files
Reliability
Location/Method
Description
MOST LIKELY
Credit card type and last 4 digits
Card payment details
MOST LIKELY
"Cash," "Card," "Check" indicators
Payment type labels
MOST LIKELY
Payment method in summary section
Payment information area
POSSIBLE
Bank transfer details
Electronic payment info
POSSIBLE
Digital payment indicators (PayPal, etc.)
Online payment methods

Common File Labels/Synonyms
Payment Method, Card Type, Paid By, "Payment:", "Card:", "Method:", "Paid:", or direct payment indicators without labels (e.g., "VISA ****1234", "Cash", "American Express")
9. Item Description/Line Items
Definition: Detailed breakdown of goods or services purchased.
How to Recognize on Files
Reliability
Location/Method
Description
Most Likely
Some description of what was purchased
Must be present in some form
MOST LIKELY
Itemized list in document body
Detailed product/service breakdown
MOST LIKELY
Product/service descriptions
Clear item identification
MOST LIKELY
Quantity and unit price details
Pricing breakdown
POSSIBLE
Summary descriptions only
General category information
POSSIBLE
Category codes instead of descriptions
Abbreviated item references

Common File Labels/Synonyms
Description, Item, Product, Service, Details, "Description:", "Item:", "Product:", "Service:", or direct product names without labels (e.g., "iPhone 17", "Office Chair", "Consulting Services")

"""
    
    # Get the response from the agent
    response = issue_detection_agent.run(formatted_prompt)

    # Debug logging
    print(f"DEBUG: Compliance response type: {type(response)}")
    print(f"DEBUG: Compliance response has content: {hasattr(response, 'content')}")
    if hasattr(response, 'content'):
        print(f"DEBUG: Compliance content type: {type(response.content)}")
        print(f"DEBUG: Compliance content length: {len(response.content) if response.content else 'None'}")
        print(f"DEBUG: Compliance content preview: {response.content[:200] if response.content else 'None'}")
        
    # Validate the compliance response using UQLM
    try:
        vals = ExpenseComplianceUQLMValidator(primary_llm=llm_client, logger=logger)

        validation_results = await vals.validate_compliance_response(
            ai_response=response.content,
            country=country,
            icp=icp,
            receipt_type=receipt_type,
            compliance_json=compliance_json,
            extracted_json=extracted_json
        )

        logger.info(f"✅ UQLM validation completed with confidence: {validation_results['validation_summary']['overall_confidence']:.2f}")
        logger.info(f"📊 Reliability level: {validation_results['validation_summary']['reliability_level']}")

        # Store validation results in a way that can be accessed by the workflow
        # Since we can't modify the response object, we'll return both the response and validation results
        return response, validation_results

    except Exception as e:
        logger.error(f"❌ UQLM validation failed: {str(e)}")
        logger.warning("⚠️ Continuing without validation results")
        return response, None




