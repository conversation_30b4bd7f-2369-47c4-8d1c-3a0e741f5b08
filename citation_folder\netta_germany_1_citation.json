{"citations": {"supplier_name": {"field_citation": {"source_text": "From", "confidence": 0.9, "source_location": "markdown", "context": "## From\nSaldo Apps", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON>", "confidence": 0.95, "source_location": "markdown", "context": "## From\nSaldo Apps", "match_type": "exact"}}, "supplier_address": {"field_citation": {"source_text": "From", "confidence": 0.9, "source_location": "markdown", "context": "## From\n<PERSON><PERSON> A<PERSON>\nFirst str. 28-32, Chicago USA", "match_type": "contextual"}, "value_citation": {"source_text": "First str. 28-32, Chicago USA", "confidence": 0.95, "source_location": "markdown", "context": "First str. 28-32, Chicago USA", "match_type": "exact"}}, "currency": {"field_citation": {"source_text": "RATE, USD", "confidence": 0.85, "source_location": "markdown", "context": "| DESCRIPTION | RATE, USD | QTY | TAX, % | DISC, % | AMOUNT, USD |", "match_type": "exact"}, "value_citation": {"source_text": "USD", "confidence": 0.95, "source_location": "markdown", "context": "Subtotal: | USD 8,200.00\nDiscount (20%): | USD 0.00\nShipping Cost: | USD 0.00\nSales Tax: | USD 460.00\nTotal: | USD 8,660.00", "match_type": "exact"}}, "invoice_number": {"field_citation": {"source_text": "Invoice no.:", "confidence": 0.95, "source_location": "markdown", "context": "| Invoice no.: | 001 |", "match_type": "exact"}, "value_citation": {"source_text": "001", "confidence": 0.95, "source_location": "markdown", "context": "| Invoice no.: | 001 |", "match_type": "exact"}}, "invoice_date": {"field_citation": {"source_text": "Invoice date:", "confidence": 0.95, "source_location": "markdown", "context": "| Invoice date: | Jul 13th, 2021 |", "match_type": "exact"}, "value_citation": {"source_text": "Jul 13th, 2021", "confidence": 0.9, "source_location": "markdown", "context": "| Invoice date: | Jul 13th, 2021 |", "match_type": "exact"}}, "due_date": {"field_citation": {"source_text": "Due:", "confidence": 0.95, "source_location": "markdown", "context": "| Due: | Feb 28th, 2022 |", "match_type": "exact"}, "value_citation": {"source_text": "Feb 28th, 2022", "confidence": 0.9, "source_location": "markdown", "context": "| Due: | Feb 28th, 2022 |", "match_type": "exact"}}, "contact_email": {"field_citation": {"source_text": "contact", "confidence": 0.85, "source_location": "markdown", "context": "<EMAIL>", "match_type": "contextual"}, "value_citation": {"source_text": "<EMAIL>", "confidence": 0.95, "source_location": "markdown", "context": "<EMAIL>", "match_type": "exact"}}, "contact_phone": {"field_citation": {"source_text": "contact", "confidence": 0.85, "source_location": "markdown", "context": "5026697967", "match_type": "contextual"}, "value_citation": {"source_text": "5026697967", "confidence": 0.95, "source_location": "markdown", "context": "5026697967", "match_type": "exact"}}, "contact_website": {"field_citation": {"source_text": "contact", "confidence": 0.85, "source_location": "markdown", "context": "saldoapps.com", "match_type": "contextual"}, "value_citation": {"source_text": "saldoapps.com", "confidence": 0.95, "source_location": "markdown", "context": "saldoapps.com", "match_type": "exact"}}, "subtotal": {"field_citation": {"source_text": "Subtotal:", "confidence": 0.9, "source_location": "markdown", "context": "| Subtotal: | USD 8,200.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 8,200.00", "confidence": 0.95, "source_location": "markdown", "context": "| Subtotal: | USD 8,200.00 |", "match_type": "exact"}}, "discount": {"field_citation": {"source_text": "Discount (20%):", "confidence": 0.9, "source_location": "markdown", "context": "| Discount (20%): | USD 0.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 0.00", "confidence": 0.95, "source_location": "markdown", "context": "| Discount (20%): | USD 0.00 |", "match_type": "exact"}}, "shipping_cost": {"field_citation": {"source_text": "Shipping Cost:", "confidence": 0.9, "source_location": "markdown", "context": "| Shipping Cost: | USD 0.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 0.00", "confidence": 0.95, "source_location": "markdown", "context": "| Shipping Cost: | USD 0.00 |", "match_type": "exact"}}, "sales_tax": {"field_citation": {"source_text": "Sales Tax:", "confidence": 0.9, "source_location": "markdown", "context": "| Sales Tax: | USD 460.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 460.00", "confidence": 0.95, "source_location": "markdown", "context": "| Sales Tax: | USD 460.00 |", "match_type": "exact"}}, "total_amount": {"field_citation": {"source_text": "Total:", "confidence": 0.9, "source_location": "markdown", "context": "| Total: | USD 8,660.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 8,660.00", "confidence": 0.95, "source_location": "markdown", "context": "| Total: | USD 8,660.00 |", "match_type": "exact"}}, "amount_paid": {"field_citation": {"source_text": "Amount paid:", "confidence": 0.9, "source_location": "markdown", "context": "| Amount paid: | USD 0.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 0.00", "confidence": 0.95, "source_location": "markdown", "context": "| Amount paid: | USD 0.00 |", "match_type": "exact"}}, "balance_due": {"field_citation": {"source_text": "Balance Due:", "confidence": 0.9, "source_location": "markdown", "context": "| Balance Due: | USD 8,660.00 |", "match_type": "exact"}, "value_citation": {"source_text": "USD 8,660.00", "confidence": 0.95, "source_location": "markdown", "context": "| Balance Due: | USD 8,660.00 |", "match_type": "exact"}}}, "metadata": {"total_fields_analyzed": 5, "fields_with_field_citations": 5, "fields_with_value_citations": 5, "average_confidence": 0.933}}