#!/usr/bin/env python3
"""
Test script for schema-based file classification
"""

import json
from file_classification_agent import classify_file

def test_expense_document():
    """Test with a clear expense document."""
    
    # Sample expense document text
    expense_text = """
    INVOICE
    
    From: Starbucks Coffee Company
    123 Main Street, Vienna, Austria
    
    To: John <PERSON>
    Marketing Department
    
    Invoice #: INV-2025-001
    Date: July 16, 2025
    
    Items:
    - Large Coffee: €4.50
    - Pastry: €3.25
    
    Subtotal: €7.75
    VAT (20%): €1.55
    Total: €9.30
    
    Payment: VISA ****1234
    """
    
    print("🧪 Testing with EXPENSE document:")
    print("=" * 50)
    print(expense_text)
    print("=" * 50)
    
    result = classify_file(expense_text, "Austria")
    
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)
    
    try:
        parsed_result = json.loads(content)
        print("✅ Classification Result:")
        print(f"   Is Expense: {parsed_result.get('is_expense')}")
        print(f"   Expense Type: {parsed_result.get('expense_type')}")
        print(f"   Fields Found: {parsed_result.get('schema_field_analysis', {}).get('fields_found', [])}")
        print(f"   Total Fields: {parsed_result.get('schema_field_analysis', {}).get('total_fields_found', 0)}")
        print(f"   Reasoning: {parsed_result.get('schema_field_analysis', {}).get('expense_identification_reasoning', 'N/A')}")
        
        return parsed_result.get('is_expense', False)
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        print(f"Raw content: {content}")
        return False

def test_non_expense_document():
    """Test with a non-expense document."""
    
    # Sample non-expense document text
    non_expense_text = """
    COMPANY NEWSLETTER
    
    Welcome to our monthly newsletter!
    
    This month we're excited to announce our new product launch.
    Our team has been working hard to bring you innovative solutions.
    
    Featured Articles:
    - How to improve productivity
    - Best practices for remote work
    - Company culture updates
    
    Thank you for reading!
    
    Best regards,
    The Marketing Team
    """
    
    print("\n🧪 Testing with NON-EXPENSE document:")
    print("=" * 50)
    print(non_expense_text)
    print("=" * 50)
    
    result = classify_file(non_expense_text, "Austria")
    
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)
    
    try:
        parsed_result = json.loads(content)
        print("✅ Classification Result:")
        print(f"   Is Expense: {parsed_result.get('is_expense')}")
        print(f"   Expense Type: {parsed_result.get('expense_type')}")
        print(f"   Fields Found: {parsed_result.get('schema_field_analysis', {}).get('fields_found', [])}")
        print(f"   Total Fields: {parsed_result.get('schema_field_analysis', {}).get('total_fields_found', 0)}")
        print(f"   Reasoning: {parsed_result.get('schema_field_analysis', {}).get('expense_identification_reasoning', 'N/A')}")
        
        return not parsed_result.get('is_expense', True)  # Should be False for non-expense
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        print(f"Raw content: {content}")
        return False

def main():
    """Run classification tests."""
    
    print("🚀 Testing Schema-Based File Classification")
    print("=" * 60)
    
    try:
        # Test 1: Expense document
        expense_test_passed = test_expense_document()
        
        # Test 2: Non-expense document  
        non_expense_test_passed = test_non_expense_document()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY:")
        print(f"   Expense Document Test: {'✅ PASSED' if expense_test_passed else '❌ FAILED'}")
        print(f"   Non-Expense Document Test: {'✅ PASSED' if non_expense_test_passed else '❌ FAILED'}")
        
        if expense_test_passed and non_expense_test_passed:
            print("\n🎉 ALL TESTS PASSED! Schema-based classification is working correctly.")
            return 0
        else:
            print("\n❌ Some tests failed. Please check the classification logic.")
            return 1
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
