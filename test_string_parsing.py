#!/usr/bin/env python3
"""
Test string parsing for quality validation
"""

# Test string from actual output
test_str = "{'dimension': 'visual_accuracy', 'confidence_score': 0.95, 'issues': [], 'summary': 'test', 'reliability_level': 'high'}"

print("Testing string parsing...")
print(f"Original string: {test_str[:50]}...")

try:
    import ast
    parsed = ast.literal_eval(test_str)
    print(f"✅ ast.literal_eval worked: {type(parsed)}")
    print(f"   confidence_score: {parsed.get('confidence_score')}")
    print(f"   reliability_level: {parsed.get('reliability_level')}")
except Exception as e:
    print(f"❌ ast.literal_eval failed: {e}")
    try:
        parsed = eval(test_str)
        print(f"✅ eval worked: {type(parsed)}")
        print(f"   confidence_score: {parsed.get('confidence_score')}")
        print(f"   reliability_level: {parsed.get('reliability_level')}")
    except Exception as e2:
        print(f"❌ eval also failed: {e2}")
