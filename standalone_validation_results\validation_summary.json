{"validation_timestamp": "2025-07-17T09:40:51.963341", "compliance_validation": {"enabled": true, "results": [{"source_file": "austrian_file.json", "validation_file": "austrian_file_compliance_validation.json", "validation_time": 102.16, "confidence_score": 0.49, "reliability_level": "VERY_LOW", "status": "completed"}, {"source_file": "german_file_2.json", "validation_file": "german_file_2_compliance_validation.json", "validation_time": 95.35, "confidence_score": 0.5925, "reliability_level": "LOW", "status": "completed"}, {"source_file": "german_file_3.json", "validation_file": "german_file_3_compliance_validation.json", "validation_time": 95.01, "confidence_score": 0.7000000000000001, "reliability_level": "LOW", "status": "completed"}, {"source_file": "german_file_4.json", "validation_file": "german_file_4_compliance_validation.json", "validation_time": 92.31, "confidence_score": 0.98, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "german_file_5.json", "validation_file": "german_file_5_compliance_validation.json", "validation_time": 94.32, "confidence_score": 0.8350000000000001, "reliability_level": "MEDIUM", "status": "completed"}, {"source_file": "german_file_6.json", "validation_file": "german_file_6_compliance_validation.json", "validation_time": 89.42, "confidence_score": 0.9299999999999999, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "german_file_7.json", "validation_file": "german_file_7_compliance_validation.json", "validation_time": 92.56, "confidence_score": 0.9824999999999999, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "german_file_8.json", "validation_file": "german_file_8_compliance_validation.json", "validation_time": 84.35, "confidence_score": 0.9099999999999999, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "german_file_9.json", "validation_file": "german_file_9_compliance_validation.json", "validation_time": 99.71, "confidence_score": 0.81, "reliability_level": "MEDIUM", "status": "completed"}, {"source_file": "italia_file.json", "validation_file": "italia_file_compliance_validation.json", "validation_time": 101.06, "confidence_score": 0.6375000000000001, "reliability_level": "LOW", "status": "completed"}, {"source_file": "italia_file_2.json", "validation_file": "italia_file_2_compliance_validation.json", "validation_time": 97.58, "confidence_score": 0.98, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "italia_file_3.json", "validation_file": "italia_file_3_compliance_validation.json", "validation_time": 91.73, "confidence_score": 1.0, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "italia_file_4.json", "validation_file": "italia_file_4_compliance_validation.json", "validation_time": 105.92, "confidence_score": 0.9449999999999998, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "italia_file_5.json", "validation_file": "italia_file_5_compliance_validation.json", "validation_time": 88.4, "confidence_score": 0.86, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "italia_file_6.json", "validation_file": "italia_file_6_compliance_validation.json", "validation_time": 94.5, "confidence_score": 0.9824999999999999, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "netta_austria_1.json", "validation_file": "netta_austria_1_compliance_validation.json", "validation_time": 94.67, "confidence_score": 0.7825, "reliability_level": "MEDIUM", "status": "completed"}, {"source_file": "netta_austria_2.json", "validation_file": "netta_austria_2_compliance_validation.json", "validation_time": 87.13, "confidence_score": 0.9924999999999999, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "netta_germany_1.json", "validation_file": "netta_germany_1_compliance_validation.json", "validation_time": 91.7, "confidence_score": 0.9575, "reliability_level": "MEDIUM", "status": "completed"}, {"source_file": "netta_germany_2.json", "validation_file": "netta_germany_2_compliance_validation.json", "validation_time": 102.77, "confidence_score": 0.65, "reliability_level": "LOW", "status": "completed"}, {"source_file": "netta_germany_3.json", "validation_file": "netta_germany_3_compliance_validation.json", "validation_time": 97.13, "confidence_score": 0.8, "reliability_level": "MEDIUM", "status": "completed"}, {"source_file": "netta_italy_1.json", "validation_file": "netta_italy_1_compliance_validation.json", "validation_time": 96.88, "confidence_score": 0.7475, "reliability_level": "LOW", "status": "completed"}, {"source_file": "netta_italy_2.json", "validation_file": "netta_italy_2_compliance_validation.json", "validation_time": 98.36, "confidence_score": 0.9675, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "netta_italy_3.json", "validation_file": "netta_italy_3_compliance_validation.json", "validation_time": 134.1, "confidence_score": 0.9924999999999999, "reliability_level": "HIGH", "status": "completed"}, {"source_file": "swiss_file.json", "validation_file": "swiss_file_compliance_validation.json", "validation_time": 92.66, "confidence_score": 0.8225, "reliability_level": "MEDIUM", "status": "completed"}, {"source_file": "swiss_file_2.json", "validation_file": "swiss_file_2_compliance_validation.json", "validation_time": 93.94, "confidence_score": 0.6375000000000001, "reliability_level": "VERY_LOW", "status": "completed"}, {"source_file": "swiss_file_3.json", "validation_file": "swiss_file_3_compliance_validation.json", "validation_time": 93.09, "confidence_score": 0.815, "reliability_level": "MEDIUM", "status": "completed"}]}, "quality_validation": {"enabled": true, "results": []}, "total_files_processed": 26, "total_validation_time": 2508.71}