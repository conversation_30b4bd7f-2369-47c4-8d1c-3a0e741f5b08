# Single File Validation Test

This script demonstrates the new validation format transformation by running both compliance validation and LLM image quality assessment validation on a single file.

## Usage

### Basic Usage
```bash
# Test both compliance and quality validation
python test_single_file_validation.py --file austrian_file

# Test only compliance validation
python test_single_file_validation.py --file german_file_2 --skip-quality

# Test only quality validation  
python test_single_file_validation.py --file austrian_file --skip-compliance
```

### Available Files
Check your `results/` directory for available files:
- `austrian_file`
- `german_file_2`
- `german_file_3`
- etc.

## What It Does

### 1. Compliance Validation
- Loads the result file from `results/{file_name}.json`
- Extracts compliance and extraction data
- Runs UQLM compliance validation using the same validators as the main workflow
- **Transforms raw UQLM output to expected format** (same as integrated workflow)
- Saves result to `test_validation_output/{file_name}_compliance_validation.json`

### 2. LLM Image Quality Assessment Validation
- Loads quality assessment from `llm_quality_reports/{file_name}_quality_assessment.json`
- Finds corresponding image in `dataset/` directory
- Runs UQLM quality validation
- **Transforms raw UQLM output to expected format**
- Saves result to `test_validation_output/{file_name}_quality_validation.json`

## Output Format

The script produces validation results in the **same format** as the integrated workflow:

```json
{
  "validation_report": {
    "timestamp": "2025-07-17T...",
    "overall_assessment": {
      "confidence_score": 0.7325,
      "reliability_level": "MEDIUM",
      "is_reliable": true,
      "recommendation": "..."
    },
    "critical_issues_summary": {
      "total_issues": 10,
      "issues": [...]
    },
    "dimensional_analysis_summary": {
      "factual_grounding": {
        "confidence": 0.85,
        "reliability": "high",
        "issues_count": 1
      }
    }
  },
  "detailed_analysis": {
    "metadata": {
      "country": "Austria",
      "receipt_type": "flights",
      "icp": "Global People",
      "validation_method": "UQLM LLMPanel",
      "panel_judges": 2,
      "original_issues_found": 5
    },
    "dimension_details": {
      "factual_grounding": {
        "confidence_score": 0.85,
        "reliability_level": "high",
        "summary": "...",
        "issues_found": [...],
        "total_issues": 1
      }
    }
  }
}
```

## Output Files

All results are saved to `test_validation_output/`:
- `{file_name}_compliance_validation.json` - Compliance validation result
- `{file_name}_quality_validation.json` - Quality validation result  
- `{file_name}_test_summary.json` - Test execution summary

## Example Output

```
🧪 Testing Validation on Single File: austrian_file
============================================================

📋 Testing Compliance Validation
   📁 Loading result file: austrian_file.json
   📊 Metadata: Austria | flights | Global People
   🕐 Running UQLM compliance validation...
   🔄 Transforming to expected format...
   ✅ Compliance validation completed in 15.23s
   📊 Confidence: 0.485 | Reliability: VERY_LOW
   💾 Saved: austrian_file_compliance_validation.json

🖼️ Testing LLM Image Quality Assessment Validation
   📁 Loading quality file: austrian_file_quality_assessment.json
   🖼️ Found image: austrian_file.jpg
   🕐 Running UQLM quality validation...
   🔄 Transforming to expected format...
   ✅ Quality validation completed in 12.45s
   📊 Confidence: 0.825 | Reliability: HIGH
   💾 Saved: austrian_file_quality_validation.json

📊 Test Summary:
   Total time: 27.68 seconds
   Compliance validation: completed
   Quality validation: completed
   Summary saved: austrian_file_test_summary.json
```

## Verification

Compare the output files with the expected format from `validation_results copy/` to verify they match the integrated workflow format.

The key difference is that this script uses the **new transformation functions** that convert raw UQLM output to the expected format, demonstrating that the standalone validation runner now produces the same results as the integrated workflow.
