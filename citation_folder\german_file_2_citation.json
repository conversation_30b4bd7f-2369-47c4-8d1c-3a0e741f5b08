{"citations": {"supplier_name": {"field_citation": {"source_text": "Pizzeria Pisa", "confidence": 0.9, "source_location": "markdown", "context": "# Pizzeria Pisa\nCora-Berliner Str.2", "match_type": "exact"}, "value_citation": {"source_text": "Pizzeria Pisa", "confidence": 0.9, "source_location": "markdown", "context": "# Pizzeria Pisa\nCora-Berliner Str.2", "match_type": "exact"}}, "supplier_address": {"field_citation": {"source_text": "Cora-Berliner Str.2, 10117 Berlin", "confidence": 0.8, "source_location": "markdown", "context": "Cora-Berliner Str.2\n10117 Berlin", "match_type": "exact"}, "value_citation": {"source_text": "Cora-Berliner Str.2, 10117 Berlin", "confidence": 0.8, "source_location": "markdown", "context": "Cora-Berliner Str.2\n10117 Berlin", "match_type": "exact"}}, "vat_number": {"field_citation": {"source_text": "St.Nr.:", "confidence": 0.8, "source_location": "markdown", "context": "St.Nr.:34/476/00588", "match_type": "contextual"}, "value_citation": {"source_text": "34/476/00588", "confidence": 0.9, "source_location": "markdown", "context": "St.Nr.:34/476/00588", "match_type": "exact"}}, "currency": {"field_citation": {"source_text": "EUR", "confidence": 0.7, "source_location": "markdown", "context": "3,60 EUR", "match_type": "contextual"}, "value_citation": {"source_text": "EUR", "confidence": 0.7, "source_location": "markdown", "context": "3,60 EUR", "match_type": "contextual"}}, "total_amount": {"field_citation": {"source_text": "<PERSON><PERSON>", "confidence": 0.8, "source_location": "markdown", "context": "Saldo 9,50 EUR", "match_type": "exact"}, "value_citation": {"source_text": "9,50 EUR", "confidence": 0.9, "source_location": "markdown", "context": "Saldo 9,50 EUR", "match_type": "exact"}}, "date_of_issue": {"field_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 0.8, "source_location": "markdown", "context": "# <PERSON><PERSON>nung\nTisch #120", "match_type": "contextual"}, "value_citation": {"source_text": "20.10.2014", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "exact"}}, "line_items": {"field_citation": {"source_text": "0,4 Cola Light | 3,60 EUR", "confidence": 0.9, "source_location": "markdown", "context": "| 0,4 Cola Light    | 3,60 EUR |", "match_type": "exact"}, "value_citation": {"source_text": "Currywurst Pommes | 5,90 EUR", "confidence": 0.9, "source_location": "markdown", "context": "| Currywurst Pommes | 5,90 EUR |", "match_type": "exact"}}, "table_number": {"field_citation": {"source_text": "Tisch #120", "confidence": 0.9, "source_location": "markdown", "context": "Tisch #120", "match_type": "exact"}, "value_citation": {"source_text": "120", "confidence": 0.9, "source_location": "markdown", "context": "Tisch #120", "match_type": "exact"}}, "net_total": {"field_citation": {"source_text": "Nettoumsatz", "confidence": 0.9, "source_location": "markdown", "context": "Nettoumsatz 7,98 EUR", "match_type": "exact"}, "value_citation": {"source_text": "7,98 EUR", "confidence": 0.9, "source_location": "markdown", "context": "Nettoumsatz 7,98 EUR", "match_type": "exact"}}, "tax_rate": {"field_citation": {"source_text": "MWST 19%", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19% 1,52 EUR", "match_type": "exact"}, "value_citation": {"source_text": "19%", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19% 1,52 EUR", "match_type": "exact"}}, "vat": {"field_citation": {"source_text": "MWST 19%", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19% 1,52 EUR", "match_type": "exact"}, "value_citation": {"source_text": "1,52 EUR", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19% 1,52 EUR", "match_type": "exact"}}, "transaction_time": {"field_citation": {"source_text": "13:45", "confidence": 0.8, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "contextual"}, "value_citation": {"source_text": "13:45", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "exact"}}, "operator_id": {"field_citation": {"source_text": "<PERSON><PERSON><PERSON>", "confidence": 0.8, "source_location": "markdown", "context": "Es bediente Sie Bediener 3.", "match_type": "contextual"}, "value_citation": {"source_text": "3", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "exact"}}, "special_notes": {"field_citation": {"source_text": "Tip is not included", "confidence": 0.9, "source_location": "markdown", "context": "Tip is not included", "match_type": "exact"}, "value_citation": {"source_text": "Tip is not included", "confidence": 0.9, "source_location": "markdown", "context": "Tip is not included", "match_type": "exact"}}, "payment_method": {"field_citation": {"source_text": "Bar", "confidence": 0.8, "source_location": "markdown", "context": "Bar 9,50 EUR", "match_type": "contextual"}, "value_citation": {"source_text": "Bar", "confidence": 0.8, "source_location": "markdown", "context": "Bar 9,50 EUR", "match_type": "contextual"}}, "receipt_type": {"field_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 0.9, "source_location": "markdown", "context": "# Rechnung", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 0.9, "source_location": "markdown", "context": "# Rechnung", "match_type": "exact"}}}, "metadata": {"total_fields_analyzed": 16, "fields_with_field_citations": 16, "fields_with_value_citations": 16, "average_confidence": 0.875}}