{"citations": {"supplier_name": {"field_citation": {"source_text": "# Pizzeria Pisa", "confidence": 0.9, "source_location": "markdown", "context": "# Pizzeria Pisa\nCora-Berliner Str.2\n10117 Berlin", "match_type": "exact"}, "value_citation": {"source_text": "Pizzeria Pisa", "confidence": 0.9, "source_location": "markdown", "context": "# Pizzeria Pisa\nCora-Berliner Str.2\n10117 Berlin", "match_type": "exact"}}, "supplier_address": {"field_citation": {"source_text": "Cora-Berliner Str.2", "confidence": 0.8, "source_location": "markdown", "context": "# Pizzeria Pisa\nCora-Berliner Str.2\n10117 Berlin", "match_type": "contextual"}, "value_citation": {"source_text": "Cora-Berliner Str.2, 10117 Berlin", "confidence": 0.9, "source_location": "markdown", "context": "# Pizzeria Pisa\nCora-Berliner Str.2\n10117 Berlin", "match_type": "fuzzy"}}, "currency": {"field_citation": {"source_text": "EUR", "confidence": 0.9, "source_location": "markdown", "context": "| 0,4 Cola Light    | 3,60 EUR |\n| ----------------- | -------- |\n| <PERSON><PERSON><PERSON>mmes | 5,90 EUR |\nSaldo                9,50 EUR", "match_type": "inferred"}, "value_citation": {"source_text": "EUR", "confidence": 0.9, "source_location": "markdown", "context": "| 0,4 Cola Light    | 3,60 EUR |\n| ----------------- | -------- |\n| <PERSON><PERSON><PERSON>mmes | 5,90 EUR |\nSaldo                9,50 EUR", "match_type": "exact"}}, "total_amount": {"field_citation": {"source_text": "<PERSON><PERSON>", "confidence": 0.9, "source_location": "markdown", "context": "Saldo                9,50 EUR", "match_type": "exact"}, "value_citation": {"source_text": "9,50 EUR", "confidence": 0.9, "source_location": "markdown", "context": "Saldo                9,50 EUR", "match_type": "fuzzy"}}, "date_of_issue": {"field_citation": {"source_text": "20.10.2014", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "contextual"}, "value_citation": {"source_text": "20.10.2014", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "exact"}}, "transaction_time": {"field_citation": {"source_text": "13:45", "confidence": 0.8, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "contextual"}, "value_citation": {"source_text": "13:45", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "exact"}}, "table_number": {"field_citation": {"source_text": "Tisch #120", "confidence": 0.9, "source_location": "markdown", "context": "# <PERSON><PERSON>nung\n\nTisch #120", "match_type": "exact"}, "value_citation": {"source_text": "120", "confidence": 0.9, "source_location": "markdown", "context": "Tisch #120", "match_type": "fuzzy"}}, "special_notes": {"field_citation": {"source_text": "Tip is not included", "confidence": 0.9, "source_location": "markdown", "context": "Es bediente Sie Bediener 3.\nVielen Dank\n\nTip is not included", "match_type": "exact"}, "value_citation": {"source_text": "Tip is not included", "confidence": 0.9, "source_location": "markdown", "context": "Es bediente Sie Bediener 3.\nVielen Dank\n\nTip is not included", "match_type": "exact"}}, "nettoumsatz": {"field_citation": {"source_text": "Nettoumsatz", "confidence": 0.9, "source_location": "markdown", "context": "Nettoumsatz          7,98 EUR", "match_type": "exact"}, "value_citation": {"source_text": "7,98 EUR", "confidence": 0.9, "source_location": "markdown", "context": "Nettoumsatz          7,98 EUR", "match_type": "fuzzy"}}, "mwst": {"field_citation": {"source_text": "MWST 19%", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19%             1,52 EUR", "match_type": "exact"}, "value_citation": {"source_text": "1,52 EUR", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19%             1,52 EUR", "match_type": "fuzzy"}}, "tax_rate": {"field_citation": {"source_text": "MWST 19%", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19%             1,52 EUR", "match_type": "exact"}, "value_citation": {"source_text": "19%", "confidence": 0.9, "source_location": "markdown", "context": "MWST 19%             1,52 EUR", "match_type": "fuzzy"}}, "payment_method": {"field_citation": {"source_text": "Bar", "confidence": 0.9, "source_location": "markdown", "context": "Bar                  9,50 EUR", "match_type": "exact"}, "value_citation": {"source_text": "Bar", "confidence": 0.9, "source_location": "markdown", "context": "Bar                  9,50 EUR", "match_type": "exact"}}, "server_info": {"field_citation": {"source_text": "Bediener 3", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "contextual"}, "value_citation": {"source_text": "Bediener 3", "confidence": 0.9, "source_location": "markdown", "context": "13:45 20.10.2014 Bediener 3", "match_type": "exact"}}}, "metadata": {"total_fields_analyzed": 12, "fields_with_field_citations": 12, "fields_with_value_citations": 12, "average_confidence": 0.9}}