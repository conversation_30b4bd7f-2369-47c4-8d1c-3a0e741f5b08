#!/usr/bin/env python3
"""
Test script to verify JSON serialization fix for ComplianceValidationResult
"""

import json
from dataclasses import dataclass
from enum import Enum
from typing import List

# Mock the classes to test serialization
class ValidationDimension(Enum):
    FACTUAL_GROUNDING = "factual_grounding"
    KNOWLEDGE_BASE_ADHERENCE = "knowledge_base_adherence"

@dataclass
class ComplianceValidationResult:
    dimension: ValidationDimension
    confidence_score: float
    issues: List[str]
    summary: str
    raw_response: str
    reliability_level: str
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "dimension": self.dimension.value if hasattr(self.dimension, 'value') else str(self.dimension),
            "confidence_score": self.confidence_score,
            "issues": self.issues,
            "summary": self.summary,
            "raw_response": self.raw_response,
            "reliability_level": self.reliability_level,
            "total_issues": len(self.issues)
        }

def make_json_serializable(obj):
    """Convert objects to JSON-serializable format."""
    if hasattr(obj, '__dict__'):
        # Convert dataclass or object to dict
        if hasattr(obj, 'to_dict'):
            return obj.to_dict()
        else:
            return {k: make_json_serializable(v) for k, v in obj.__dict__.items()}
    elif isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [make_json_serializable(item) for item in obj]
    else:
        return obj

def test_json_serialization_fix():
    """Test that ComplianceValidationResult can be JSON serialized."""
    
    print("🧪 Testing JSON Serialization Fix")
    print("=" * 50)
    
    # Create a mock validation result
    validation_result = ComplianceValidationResult(
        dimension=ValidationDimension.FACTUAL_GROUNDING,
        confidence_score=0.85,
        issues=["Issue 1", "Issue 2"],
        summary="Test summary",
        raw_response="Test raw response",
        reliability_level="high"
    )
    
    print("📋 Test 1: Direct JSON Serialization")
    try:
        # This should fail without to_dict()
        json_str = json.dumps(validation_result)
        print("   ❌ Direct serialization should have failed but didn't")
        return False
    except TypeError as e:
        print(f"   ✅ Direct serialization failed as expected: {str(e)[:50]}...")
    
    print("\n📋 Test 2: to_dict() Method")
    try:
        result_dict = validation_result.to_dict()
        print(f"   ✅ to_dict() method works")
        print(f"   📊 Keys: {list(result_dict.keys())}")
        print(f"   📊 Dimension: {result_dict['dimension']}")
        print(f"   📊 Confidence: {result_dict['confidence_score']}")
        print(f"   📊 Issues count: {result_dict['total_issues']}")
    except Exception as e:
        print(f"   ❌ to_dict() method failed: {str(e)}")
        return False
    
    print("\n📋 Test 3: JSON Serialization with to_dict()")
    try:
        json_str = json.dumps(result_dict, indent=2)
        print("   ✅ JSON serialization with to_dict() works")
        print(f"   📄 JSON length: {len(json_str)} characters")
    except Exception as e:
        print(f"   ❌ JSON serialization failed: {str(e)}")
        return False
    
    print("\n📋 Test 4: Helper Function Serialization")
    try:
        # Test with complex nested structure
        complex_data = {
            "validation_summary": {"confidence": 0.8},
            "dimensional_analysis": {
                "factual_grounding": validation_result,
                "knowledge_base": validation_result
            },
            "metadata": {"judges": 2}
        }
        
        serializable_data = make_json_serializable(complex_data)
        json_str = json.dumps(serializable_data, indent=2)
        
        print("   ✅ Helper function serialization works")
        print(f"   📄 Complex JSON length: {len(json_str)} characters")
        
        # Verify structure is preserved
        parsed_back = json.loads(json_str)
        factual_data = parsed_back["dimensional_analysis"]["factual_grounding"]
        
        if factual_data["dimension"] == "factual_grounding":
            print("   ✅ Dimension enum converted correctly")
        else:
            print(f"   ❌ Dimension conversion failed: {factual_data['dimension']}")
            return False
            
        if factual_data["confidence_score"] == 0.85:
            print("   ✅ Confidence score preserved")
        else:
            print(f"   ❌ Confidence score not preserved: {factual_data['confidence_score']}")
            return False
            
    except Exception as e:
        print(f"   ❌ Helper function serialization failed: {str(e)}")
        return False
    
    print("\n🎯 Test Result: ✅ ALL TESTS PASSED!")
    print("🎉 JSON serialization fix is working correctly!")
    print("   - ComplianceValidationResult has to_dict() method")
    print("   - Helper function handles nested objects")
    print("   - Enum values converted to strings")
    print("   - All data preserved during serialization")
    
    return True

def main():
    """Run the test."""
    
    try:
        success = test_json_serialization_fix()
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
